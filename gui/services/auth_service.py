"""
Authentication service for user login and management.
"""
import hashlib
from datetime import datetime
from facial_recognition_system.local_database import get_connection


def hash_password(password: str) -> str:
    """Hash a password using SHA-256."""
    return hashlib.sha256(password.encode()).hexdigest()


def verify_password(password: str, password_hash: str) -> bool:
    """Verify a password against its hash."""
    return hash_password(password) == password_hash


def authenticate_user(username: str, password: str) -> dict:
    """
    Authenticate a user with username and password.
    
    Args:
        username (str): The username
        password (str): The password
        
    Returns:
        dict: User information if authentication successful, None otherwise
    """
    try:
        conn = get_connection()
        cursor = conn.cursor()
        
        cursor.execute(
            "SELECT id, username, password_hash, user_type, created_at FROM users WHERE username = ?",
            (username,)
        )
        user_row = cursor.fetchone()
        
        if not user_row:
            return None
            
        # Verify password
        if verify_password(password, user_row['password_hash']):
            return {
                'id': user_row['id'],
                'username': user_row['username'],
                'user_type': user_row['user_type'],
                'created_at': user_row['created_at']
            }
        
        return None
    except Exception:
        return None


def create_teacher_account(username: str, password: str) -> bool:
    """
    Create a new teacher account.
    
    Args:
        username (str): The username for the new teacher
        password (str): The password for the new teacher
        
    Returns:
        bool: True if account created successfully, False otherwise
    """
    try:
        conn = get_connection()
        cursor = conn.cursor()
        
        # Check if username already exists
        cursor.execute("SELECT COUNT(*) as count FROM users WHERE username = ?", (username,))
        if cursor.fetchone()['count'] > 0:
            return False
            
        # Create new teacher account
        password_hash = hash_password(password)
        now = datetime.now().isoformat()
        
        cursor.execute(
            "INSERT INTO users (username, password_hash, user_type, created_at) VALUES (?, ?, ?, ?)",
            (username, password_hash, "teacher", now)
        )
        conn.commit()
        
        return True
    except Exception:
        return False


def get_all_teachers() -> list:
    """
    Get all teacher accounts.
    
    Returns:
        list: List of teacher account information
    """
    try:
        conn = get_connection()
        cursor = conn.cursor()
        
        cursor.execute(
            "SELECT id, username, created_at FROM users WHERE user_type = 'teacher' ORDER BY created_at DESC"
        )
        rows = cursor.fetchall()
        
        return [dict(row) for row in rows]
    except Exception:
        return []


def delete_teacher_account(teacher_id: int) -> bool:
    """
    Delete a teacher account.
    
    Args:
        teacher_id (int): The ID of the teacher to delete
        
    Returns:
        bool: True if account deleted successfully, False otherwise
    """
    try:
        conn = get_connection()
        cursor = conn.cursor()
        
        cursor.execute("DELETE FROM users WHERE id = ? AND user_type = 'teacher'", (teacher_id,))
        conn.commit()
        
        return cursor.rowcount > 0
    except Exception:
        return False


def change_password(user_id: int, new_password: str) -> bool:
    """
    Change a user's password.
    
    Args:
        user_id (int): The user's ID
        new_password (str): The new password
        
    Returns:
        bool: True if password changed successfully, False otherwise
    """
    try:
        conn = get_connection()
        cursor = conn.cursor()
        
        password_hash = hash_password(new_password)
        now = datetime.now().isoformat()
        
        cursor.execute(
            "UPDATE users SET password_hash = ?, updated_at = ? WHERE id = ?",
            (password_hash, now, user_id)
        )
        conn.commit()
        
        return cursor.rowcount > 0
    except Exception:
        return False
