"""
Admin view for managing teacher accounts.
"""
import flet as ft
from gui.config.constants import ROUTE_LOGIN, ROUTE_DASHBOARD
from gui.config.language import get_text
from gui.services.auth_service import create_teacher_account, get_all_teachers, delete_teacher_account



def create_admin_view(page: ft.Page):
    """Create the admin page for managing teacher accounts."""
    is_mobile = getattr(page, 'is_mobile', False)
    current_language = getattr(page, 'language', 'fr')

    # Check if user is authenticated and is admin
    if not hasattr(page.app_state, 'current_user') or not page.app_state.current_user:
        page.go(ROUTE_LOGIN)
        return ft.View(route="/admin", controls=[])

    if page.app_state.current_user['user_type'] != 'admin':
        page.go(ROUTE_DASHBOARD)
        return ft.View(route="/admin", controls=[])

    # Teacher list container
    teachers_list = ft.Column(spacing=10)

    # Create teacher form fields
    new_username_field = ft.TextField(
        label=get_text("teacher_username", current_language),
        width=300,
        prefix_icon=ft.Icons.PERSON
    )

    new_password_field = ft.TextField(
        label=get_text("teacher_password", current_language),
        width=300,
        prefix_icon=ft.Icons.LOCK,
        password=True,
        can_reveal_password=True
    )

    # Status message
    status_message = ft.Text(
        "",
        size=14,
        visible=False
    )

    def show_message(message, is_error=False):
        """Show status message."""
        status_message.value = message
        status_message.color = ft.Colors.RED if is_error else ft.Colors.GREEN
        status_message.visible = True
        page.update()

    def hide_message():
        """Hide status message."""
        status_message.visible = False
        page.update()

    def refresh_teachers_list():
        """Refresh the teachers list."""
        teachers_list.controls.clear()
        teachers = get_all_teachers()

        if not teachers:
            teachers_list.controls.append(
                ft.Text(
                    "No teacher accounts found",
                    color=ft.Colors.GREY_600,
                    italic=True
                )
            )
        else:
            for teacher in teachers:
                teacher_card = ft.Card(
                    content=ft.Container(
                        content=ft.Row([
                            ft.Icon(ft.Icons.PERSON, color=ft.Colors.BLUE_600),
                            ft.Column([
                                ft.Text(
                                    teacher['username'],
                                    weight=ft.FontWeight.BOLD,
                                    size=16
                                ),
                                ft.Text(
                                    f"Created: {teacher['created_at'][:10]}",
                                    color=ft.Colors.GREY_600,
                                    size=12
                                )
                            ], spacing=2, expand=True),
                            ft.IconButton(
                                icon=ft.Icons.DELETE,
                                icon_color=ft.Colors.RED,
                                tooltip="Delete Teacher",
                                on_click=lambda e, teacher_id=teacher['id']: delete_teacher_clicked(teacher_id)
                            )
                        ], alignment=ft.MainAxisAlignment.SPACE_BETWEEN),
                        padding=15
                    ),
                    elevation=2
                )
                teachers_list.controls.append(teacher_card)

        page.update()

    def create_teacher_clicked(e):
        """Handle create teacher button click."""
        hide_message()

        username = new_username_field.value.strip()
        password = new_password_field.value

        if not username:
            show_message(get_text("please_enter_username", current_language), True)
            return

        if not password:
            show_message(get_text("please_enter_password", current_language), True)
            return

        if len(password) < 4:
            show_message(get_text("password_min_length", current_language), True)
            return

        success = create_teacher_account(username, password)

        if success:
            show_message(get_text("teacher_created_successfully", current_language))
            new_username_field.value = ""
            new_password_field.value = ""
            refresh_teachers_list()
        else:
            show_message(get_text("username_exists", current_language), True)

    def delete_teacher_clicked(teacher_id):
        """Handle delete teacher button click."""
        def confirm_delete(e):
            success = delete_teacher_account(teacher_id)
            if success:
                show_message("Teacher account deleted successfully")
                refresh_teachers_list()
            else:
                show_message("Failed to delete teacher account", True)
            page.dialog.open = False
            page.update()

        def cancel_delete(e):
            page.dialog.open = False
            page.update()

        # Show confirmation dialog
        page.dialog = ft.AlertDialog(
            modal=True,
            title=ft.Text("Confirm Delete"),
            content=ft.Text("Are you sure you want to delete this teacher account?"),
            actions=[
                ft.TextButton("Cancel", on_click=cancel_delete),
                ft.TextButton("Delete", on_click=confirm_delete, style=ft.ButtonStyle(color=ft.Colors.RED)),
            ],
            actions_alignment=ft.MainAxisAlignment.END,
        )
        page.dialog.open = True
        page.update()

    def logout_clicked(e):
        """Handle logout button click."""
        page.app_state.current_user = None
        page.app_state.is_authenticated = False
        page.go(ROUTE_LOGIN)

    # Create teacher form
    create_teacher_form = ft.Card(
        content=ft.Container(
            content=ft.Column([
                ft.Text(
                    "Create New Teacher Account",
                    size=20,
                    weight=ft.FontWeight.BOLD,
                    color=ft.Colors.BLUE_900
                ),
                ft.Container(height=15),
                new_username_field,
                ft.Container(height=10),
                new_password_field,
                ft.Container(height=10),
                status_message,
                ft.Container(height=15),
                ft.ElevatedButton(
                    text="Create Teacher",
                    icon=ft.Icons.PERSON_ADD,
                    style=ft.ButtonStyle(
                        bgcolor=ft.Colors.GREEN_600,
                        color=ft.Colors.WHITE
                    ),
                    on_click=create_teacher_clicked
                )
            ], horizontal_alignment=ft.CrossAxisAlignment.CENTER),
            padding=20
        ),
        elevation=3
    )

    # Teachers list section
    teachers_section = ft.Card(
        content=ft.Container(
            content=ft.Column([
                ft.Text(
                    "Teacher Accounts",
                    size=20,
                    weight=ft.FontWeight.BOLD,
                    color=ft.Colors.BLUE_900
                ),
                ft.Container(height=15),
                teachers_list
            ]),
            padding=20
        ),
        elevation=3
    )

    # Header with logout button
    header = ft.Row([
        ft.Text(
            "Admin Panel",
            size=28,
            weight=ft.FontWeight.BOLD,
            color=ft.Colors.BLUE_900
        ),
        ft.ElevatedButton(
            text="Logout",
            icon=ft.Icons.LOGOUT,
            style=ft.ButtonStyle(
                bgcolor=ft.Colors.RED_600,
                color=ft.Colors.WHITE
            ),
            on_click=logout_clicked
        )
    ], alignment=ft.MainAxisAlignment.SPACE_BETWEEN)

    # Main content
    main_content = ft.Column([
        header,
        ft.Container(height=20),
        create_teacher_form,
        ft.Container(height=20),
        teachers_section
    ], spacing=0, expand=True)

    # Initialize teachers list
    refresh_teachers_list()

    # Create the view
    view = ft.View(
        route="/admin",
        controls=[
            ft.Container(
                content=main_content,
                padding=20,
                expand=True
            )
        ],
        padding=0,
        spacing=0
    )

    return view
